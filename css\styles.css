/* 
/* Hero Section with Background Image */
html,body{
    overflow-x: hidden;

    width: 100%;
}
/* hero-section */
.hero {
  background: url('../images/banner-bg.png') no-repeat center;
  background-size: cover;
}
.nav-item a {
    font-weight: 600;
    font-size: 13px;
}
.nav-item a:hover {
    color: #13499f!important;
    text-decoration: none;
}
.head{
    position: relative;
    z-index: 1;
   
}
.harness h1{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    padding-left:40px;
    color:white;
    font-size: 55px;
    font-weight: 700;
    margin-top: 198px;
    line-height: 70px;
}
.harness p{
    padding-left:40px;
    color: white;
    font-size: 17px;

}
.mt-3{
    color: white;
}

/* welcome-section */
.welcome-section{
    padding: 50px 0px;
}
.welcome-img{
    position: relative;
}
.welcome-con h1{
    color:black;
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 700;
    font-size: 30px;
    line-height:45px;
}
.welcome-con h4{
    font-size: 22px;
    color: black;

}
.welcome-con p{
    font-size: 14px;
    line-height: 25px;
    color: black;
}
.small-1{
    position: absolute;
    top: 13px;
    right: 62px;
}
.small-2{
    position: absolute;
    top: 280px;
    right: 60px;

}

/* features-section */
.features-section{
    padding: 50px 0px;
    background-color: #13499f;
}
.fh{
    margin-top: 40px;
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 700;
    font-size: 30px;
    color: white;
    text-align: center;
   


}
.feature-card{
    margin-top: 35px;
    margin-bottom: 50px;
    padding: 30px;
    background-color: white;
    border-radius: 10px; 
    min-height: 540px;
    font-size: 18px;
    color: black;
    
}
.feature-card p{
    font-size: 14px;
    font-weight: 495;
    line-height: 23px;
    color: black;
}
.feature-card h3{
    padding-top: 35px;
    font-size: 16px;
    font-weight: 700;
}

/* notification-section */
.notification-section{
    background-color: #bbebff;
    padding: 50px 0px;
}


.notify-text{
    margin-top: 140px;
}
.notify-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 30px;
font-weight: 700;
}
.notify-list {
    margin-top: 10px;
    }
.notify-item{
    display: flex;
    align-items: flex-start; 
    margin-bottom: 10px;
    font-size: 1px;  
    }
.notify-item img {
    margin-right: 8px;
    margin-top: 1px;
    }
.notify-item p{
    font-size: 15px;
    color: black;

}  

/* seamless-section */
.seamless-section{
    background-color: white;
    padding: 70px 0px;
}
.seamless-text{
    margin-top: 140px;
}
.seamless-text h3{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-size: 30px;
    font-weight: 700;
}
.seamless-list {
    margin-top: 10px;
    }
.seamless-item {
    display: flex;
    align-items: flex-start; 
    margin-bottom: 10px;
    font-size: 15px;
    line-height: 23px;
    color: black;
 
    }
.seamless-item img {
    margin-right: 8px;
    margin-top: 1px;
    }


/*flexibility-section */
.flexibility-section{
    background-color: #13499f;

}

/* Left side - Full image */
.full-image{
    width: 100%;
    height: 100vh;
    object-fit: cover;
}

/* Right side - Center accordion */
.accordion-center{
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flexibility-style{
    width: 80%;
    max-width: 500px;
}

.accordion-body{
    background-color:#13499f;
    color: white;
}

/* healthcare-section */
.healthcare-section{
    background-color: #beebfe;
    padding: 50px 0px;
}
.healthcare-text{
    margin-top: 140px;
  
}
.healthcare-text h3{
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 30px;
    font-weight: 700;
    line-height: 47px;
}
.healthcare-text h4{
    font-size: 19px;
}

.healthcare-text p{
    font-size: 15px;
} 

/* empower-section */
.empower-section{
    background-color:#13499f;
    padding: 50px 50px;
}
.empower-section h1{
    padding-top: 40px;
    color: white;
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;
    
}
.empower-para{
    color: white;
    font-size: 15px;
}
.empower-card{
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    margin-top: 50px;
    min-height: 280px;
    
    
}
.empower-card h4{
    font-size: 15px;
    font-weight: 10px;
    
}
.empower-card p{
    font-size: 12px;
    color: #39657D;
}

/* Form Container */
.form {
    background-color: #0c3c92;
    padding: 30px;
    border-radius: 10px;
}

/* Labels */
.form label {
    color: white;
    font-weight: 500;
    margin-bottom: 5px;
}

/* Input Fields */
.form .form-control {
    padding: 12px 15px;
    font-size: 15px;
    margin-bottom: 20px;
}
/* Submit Button */
.form .btn{
    background-color: #4dd0ff;
    color: black;
    font-size: 18px;
    font-weight: 700;
    border-radius: 6px;
    padding: 12px;
    width: 100%;
}

/* key-feature-section */
.key-feature-section{
    background-color: white;
    padding: 30px 0px;
    text-align: center;
}
.key-feature-section h1{
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 30px;
    font-weight: 700;  
    color: #0c3c92;
}
.key-feature-section p{
    padding-top: 10px;
    font-size: 15px;
    color: black;

}
/* footer */
.footer{
    background-color: #eff6fe;
    padding: 55px 0px;
}
.footer-logo p{
    font-size: 14px;
    padding-top: 15px;

}
.footer h5{
    color:#005cb9;
    font-size: 17px;
    font-weight: 700;
    padding-top: 16px;
}

/* Footer List Styles - Consistent alignment for all icons and text */
.list-footer-Ql {
    list-style-image: url(../images/footer-icon.png);
    font-size: 14px;
    line-height: 35px;
    color: black;
    padding-left: 0;
    margin-bottom: 0;
}

.list-footer-Ql li {
    margin-bottom: 8px;
    padding-left: 5px;
}

.list-footer-Cu {
    padding-left: 0;
    margin-bottom: 0;
}

.list-footer-Cu .ad {
    list-style-image: url(../images/location-icon.png);
    font-size: 14px;
    line-height: 35px;
    color: black;
    margin-bottom: 8px;
    padding-left: 5px;
}

.list-footer-Cu .ml {
    list-style-image: url(../images/mail-icon.png);
    font-size: 14px;
    color: black;
    line-height: 35px;
    margin-bottom: 8px;
    padding-left: 5px;
}

.list-footer-Ql-2 {
    list-style-image: url(../images/footer-icon.png);
    font-size: 14px;
    line-height: 35px;
    color: black;
    padding-left: 0;
    margin-bottom: 0;
}

.list-footer-Ql-2 li {
    margin-bottom: 8px;
    padding-left: 5px;
}
.footer-bottom {
    width: 100vw;
    background: #005cb9;
    color: #fff;
    padding-top: 20px;
    text-align: center;
    margin-bottom: 0px;
}